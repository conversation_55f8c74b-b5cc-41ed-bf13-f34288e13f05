// 自动生成的字段类型定义
// 🚨 请勿手动修改此文件，运行 npm run generate:types 重新生成
// 
// 生成时间: 2025-07-29T13:03:42.639Z
// 配置版本: 3.1.0
// 数据源: data/field_mapping.json

/**
 * 规则详情表字段类型定义
 * 基于统一字段映射配置自动生成
 */

/**
 * 通用字段接口
 * 包含所有规则类型共有的字段
 */
export interface CommonFields {
  /** 规则ID - 规则的唯一标识符 */
  rule_id?: string
  /** 规则名称 - 规则的显示名称 */
  rule_name: string
  /** 一级错误类型 - 一级错误类型 */
  level1: string
  /** 二级错误类型 - 二级错误类型 */
  level2: string
  /** 三级错误类型 - 三级错误类型 */
  level3: string
  /** 错误原因 - 错误原因 */
  error_reason: string
  /** 错误程度 - 错误程度 */
  degree: string
  /** 质控依据或参考资料 - 质控依据或参考资料 */
  reference: string
  /** 具体位置描述 - 具体位置描述 */
  detail_position: string
  /** 提示字段类型 - 提示字段的类型 */
  prompted_fields3?: string
  /** 提示字段编码 - 提示字段编码 */
  prompted_fields1: string
  /** 规则类别 - 规则类别 */
  type: string
  /** 适用业务 - 适用业务 */
  pos: string
  /** 适用地区 - 适用地区 */
  applicableArea: string
  /** 默认选用 - 默认选用 */
  default_use: string
  /** 备注信息 - 备注信息 */
  remarks?: string
  /** 入参说明 - 入参说明 */
  in_illustration?: string
  /** 开始日期 - 开始日期 */
  start_date: string
  /** 结束日期 - 结束日期 */
  end_date: string
  /** 药品编码 - 药品编码 */
  yb_code: string[]
  /** 完整诊断编码 - 完整诊断编码 */
  diag_whole_code?: string[]
  /** 诊断编码前缀 - 诊断编码前缀 */
  diag_code_prefix?: string[]
  /** 诊断名称关键字 - 诊断名称关键字 */
  diag_name_keyword?: string
  /** 完整费用编码 - 完整费用编码 */
  fee_whole_code?: string[]
  /** 费用编码前缀 - 费用编码前缀 */
  fee_code_prefix?: string[]
}

/**
 * 特定字段接口
 * 包含特定规则类型的扩展字段
 */
export interface SpecificFields {
  /** 年龄阈值 - 年龄阈值 */
  age_threshold?: number
  /** 限制天数 - 限制天数 */
  limit_days?: number
  /** 中药饮片医保编码 - 中药饮片医保编码 */
  mono_use_yb_code?: string
  /** 搭配使用的药品编码前缀 - 搭配使用的药品编码前缀 */
  drug_start_with?: string
  /** 药敏试验医保代码前缀 - 药敏试验医保代码前缀 */
  drug_code_prefix?: string[]
}

/**
 * 完整规则详情接口
 * 继承通用字段，包含扩展字段
 */
export interface RuleDetail extends CommonFields {
  /** 扩展字段 - JSON格式存储的特定字段 */
  extended_fields?: SpecificFields
  
  /** 元数据字段 */
  id?: number
  created_at?: string
  updated_at?: string
}

/**
 * 规则详情创建数据接口
 */
export interface CreateRuleDetailData extends Partial<CommonFields> {
  extended_fields?: SpecificFields
}

/**
 * 规则详情更新数据接口
 */
export interface UpdateRuleDetailData extends Partial<CommonFields> {
  extended_fields?: SpecificFields
}

/**
 * 字段中文名称映射
 * 用于显示和国际化
 */
export const FIELD_CHINESE_NAMES = {
  rule_id: '规则ID',
  rule_name: '规则名称',
  level1: '一级错误类型',
  level2: '二级错误类型',
  level3: '三级错误类型',
  error_reason: '错误原因',
  degree: '错误程度',
  reference: '质控依据或参考资料',
  detail_position: '具体位置描述',
  prompted_fields3: '提示字段类型',
  prompted_fields1: '提示字段编码',
  type: '规则类别',
  pos: '适用业务',
  applicableArea: '适用地区',
  default_use: '默认选用',
  remarks: '备注信息',
  in_illustration: '入参说明',
  start_date: '开始日期',
  end_date: '结束日期',
  yb_code: '药品编码',
  diag_whole_code: '完整诊断编码',
  diag_code_prefix: '诊断编码前缀',
  diag_name_keyword: '诊断名称关键字',
  fee_whole_code: '完整费用编码',
  fee_code_prefix: '费用编码前缀',
  age_threshold: '年龄阈值',
  limit_days: '限制天数',
  mono_use_yb_code: '中药饮片医保编码',
  drug_start_with: '搭配使用的药品编码前缀',
  drug_code_prefix: '药敏试验医保代码前缀',
} as const

/**
 * 字段数据类型映射
 */
export const FIELD_DATA_TYPES = {
  rule_id: 'string',
  rule_name: 'string',
  level1: 'string',
  level2: 'string',
  level3: 'string',
  error_reason: 'text',
  degree: 'string',
  reference: 'text',
  detail_position: 'text',
  prompted_fields3: 'string',
  prompted_fields1: 'string',
  type: 'string',
  pos: 'string',
  applicableArea: 'string',
  default_use: 'string',
  remarks: 'text',
  in_illustration: 'text',
  start_date: 'string',
  end_date: 'string',
  yb_code: 'array',
  diag_whole_code: 'array',
  diag_code_prefix: 'array',
  diag_name_keyword: 'string',
  fee_whole_code: 'array',
  fee_code_prefix: 'array',
  age_threshold: 'integer',
  limit_days: 'integer',
  mono_use_yb_code: 'string',
  drug_start_with: 'string',
  drug_code_prefix: 'array',
} as const

/**
 * 字段验证规则映射
 */
export const FIELD_VALIDATION_RULES = {
  rule_id: [],
  rule_name: ["required","max_length:500"],
  level1: ["required","max_length:100"],
  level2: ["required","max_length:100"],
  level3: ["required","max_length:100"],
  error_reason: ["required"],
  degree: ["required","max_length:50"],
  reference: ["required"],
  detail_position: ["required"],
  prompted_fields3: ["max_length:100"],
  prompted_fields1: ["required","max_length:100"],
  type: ["required","max_length:100"],
  pos: ["required","max_length:100"],
  applicableArea: ["required","max_length:100"],
  default_use: ["required","max_length:50"],
  remarks: [],
  in_illustration: [],
  start_date: ["required","date_format"],
  end_date: ["required","date_format"],
  yb_code: ["required","array"],
  diag_whole_code: ["array"],
  diag_code_prefix: ["array"],
  diag_name_keyword: ["max_length:200"],
  fee_whole_code: ["array"],
  fee_code_prefix: ["array"],
  age_threshold: ["integer","min:0","max:150"],
  limit_days: ["integer","min:1","max:365"],
  mono_use_yb_code: ["max_length:50"],
  drug_start_with: ["max_length:50"],
  drug_code_prefix: ["array"],
} as const

/**
 * 获取字段的中文名称
 * @param fieldName 字段名
 * @returns 中文名称
 */
export function getFieldChineseName(fieldName: string): string {
  return FIELD_CHINESE_NAMES[fieldName as keyof typeof FIELD_CHINESE_NAMES] || fieldName
}

/**
 * 获取字段的数据类型
 * @param fieldName 字段名
 * @returns 数据类型
 */
export function getFieldDataType(fieldName: string): string {
  return FIELD_DATA_TYPES[fieldName as keyof typeof FIELD_DATA_TYPES] || 'string'
}

/**
 * 获取字段的验证规则
 * @param fieldName 字段名
 * @returns 验证规则数组
 */
export function getFieldValidationRules(fieldName: string): string[] {
  const rules = FIELD_VALIDATION_RULES[fieldName as keyof typeof FIELD_VALIDATION_RULES] || []
  return [...rules] // 转换为可变数组
}

/**
 * 检查字段是否为必填
 * @param fieldName 字段名
 * @returns 是否必填
 */
export function isFieldRequired(fieldName: string): boolean {
  const rules = getFieldValidationRules(fieldName)
  return rules.includes('required')
}

/**
 * 规则类型枚举
 */
export enum RuleType {
  CH_DRUG_DENY_USE = 'ch_drug_deny_use',
  CH_DRUG_MONO_USE = 'ch_drug_mono_use',
  DRUG_DENY_MONO_USE = 'drug_deny_mono_use',
  DRUG_EXCLUDE_DIAG = 'drug_exclude_diag',
  DRUG_LIMIT_ADULT_AND_DIAG_EXACT = 'drug_limit_adult_and_diag_exact',
  DRUG_LIMIT_ADULT_AND_DIAG_PREFIX = 'drug_limit_adult_and_diag_prefix',
  DRUG_LIMIT_CHILDREN = 'drug_limit_children',
  DRUG_LIMIT_DIAG_AND_FEE_PREFIX = 'drug_limit_diag_and_fee_prefix',
  DRUG_LIMIT_DIAG_AND_MULTI_FEE = 'drug_limit_diag_and_multi_fee',
  DRUG_LIMIT_DIAG_EXACT = 'drug_limit_diag_exact',
  DRUG_LIMIT_DIAG_PREFIX = 'drug_limit_diag_prefix',
  DRUG_LIMIT_DRUG_SENSITIVITY = 'drug_limit_drug_sensitivity',
  DRUG_LIMIT_FEE_PREFIX = 'drug_limit_fee_prefix',
  DRUG_LIMIT_FEMALE = 'drug_limit_female',
  DRUG_LIMIT_INSTITUTION_LEVEL_AND_DIAG_EXACT = 'drug_limit_institution_level_and_diag_exact',
  DRUG_LIMIT_INSTITUTION_LEVEL_AND_DIAG_PREFIX = 'drug_limit_institution_level_and_diag_prefix',
  DRUG_LIMIT_INSTITUTION_LEVEL = 'drug_limit_institution_level',
  DRUG_LIMIT_MALE = 'drug_limit_male',
  DRUG_LIMIT_MATERNITY_INSURANCE = 'drug_limit_maternity_insurance',
  DRUG_LIMIT_MAX_PAY_DAYS = 'drug_limit_max_pay_days',
  DRUG_LIMIT_NUTRITIONAL_RISK = 'drug_limit_nutritional_risk',
  DRUG_LIMIT_TWO_DIAG = 'drug_limit_two_diag',
  DRUG_LIMIT_WORK_INJURY_INSURANCE = 'drug_limit_work_injury_insurance',
}

/**
 * 规则类型中文名称映射
 */
export const RULE_TYPE_CHINESE_NAMES = {
  ch_drug_deny_use: '中药饮片单复方均不予支付',
  ch_drug_mono_use: '中药饮片单方使用不予支付',
  drug_deny_mono_use: '药品单独使用不予支付',
  drug_exclude_diag: '药品禁忌症',
  drug_limit_adult_and_diag_exact: '药品限适应症+年龄（精确匹配诊断代码）',
  drug_limit_adult_and_diag_prefix: '药品限适应症+年龄（模糊匹配诊断代码）',
  drug_limit_children: '药品儿童专用',
  drug_limit_diag_and_fee_prefix: '药品限适应症（诊断+费用开头）',
  drug_limit_diag_and_multi_fee: '药品限适应症（诊断+多个费用）',
  drug_limit_diag_exact: '药品限适应症（精确匹配诊断）',
  drug_limit_diag_prefix: '药品限适应症（模糊匹配诊断）',
  drug_limit_drug_sensitivity: '药品限适应症（药敏试验+重症感染）',
  drug_limit_fee_prefix: '药品限适应症（费用开头）',
  drug_limit_female: '药品区分性别使用（女）',
  drug_limit_institution_level_and_diag_exact: '药品限适应症+机构级别（精确匹配诊断代码）',
  drug_limit_institution_level_and_diag_prefix: '药品限适应症+机构级别（模糊匹配诊断代码）',
  drug_limit_institution_level: '药品限医疗机构级别',
  drug_limit_male: '药品区分性别使用（男）',
  drug_limit_maternity_insurance: '药品限生育保险',
  drug_limit_max_pay_days: '药品限制最大支付天数',
  drug_limit_nutritional_risk: '药品限适应症（营养风险筛查）',
  drug_limit_two_diag: '药品限适应症（两个诊断）',
  drug_limit_work_injury_insurance: '药品限工伤保险',
} as const

/**
 * 获取规则类型的中文名称
 * @param ruleType 规则类型
 * @returns 中文名称
 */
export function getRuleTypeChineseName(ruleType: string): string {
  return RULE_TYPE_CHINESE_NAMES[ruleType as keyof typeof RULE_TYPE_CHINESE_NAMES] || ruleType
}

