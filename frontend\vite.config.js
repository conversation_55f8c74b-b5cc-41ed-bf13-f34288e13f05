import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
    // 加载环境变量
    const env = loadEnv(mode, process.cwd(), '')

    const isProduction = mode === 'production'
    const isDevelopment = mode === 'development'

    return {
        plugins: [
            vue()
        ],

        // 基础路径配置，支持不同部署环境
        base: env.VITE_BASE_URL || '/',

        // 路径别名
        resolve: {
            alias: {
                '@': resolve(__dirname, 'src'),
                '@components': resolve(__dirname, 'src/components'),
                '@views': resolve(__dirname, 'src/views'),
                '@utils': resolve(__dirname, 'src/utils'),
                '@api': resolve(__dirname, 'src/api'),
                '@stores': resolve(__dirname, 'src/stores'),
                '@styles': resolve(__dirname, 'src/styles'),
                '@composables': resolve(__dirname, 'src/composables'),
                '@types': resolve(__dirname, 'src/types'),
                '@design-system': resolve(__dirname, 'src/design-system')
            }
        },

        // 构建配置
        build: {
            outDir: 'dist',
            assetsDir: 'assets',
            sourcemap: isDevelopment,
            minify: isProduction ? 'esbuild' : false,

            // 构建目标
            target: 'es2015',

            // 警告阈值
            chunkSizeWarningLimit: 1000,

            // CSS代码分割
            cssCodeSplit: true,



            // Rollup配置
            rollupOptions: {
                output: {
                    // 手动代码分割 - 解决Vue和Element Plus循环依赖问题
                    manualChunks: (id) => {
                        // 基础工具库（最先加载）
                        if (id.includes('axios') || id.includes('crypto-js') ||
                            id.includes('file-saver') || id.includes('xlsx')) {
                            return 'utils'
                        }

                        // Vue + Element Plus 统一打包（避免循环依赖）
                        if (id.includes('vue') || id.includes('vue-router') || id.includes('pinia') ||
                            id.includes('element-plus') || id.includes('@element-plus/icons-vue')) {
                            return 'vue-vendor'
                        }

                        // 应用层组件
                        if (id.includes('composables')) {
                            return 'composables'
                        }

                        if (id.includes('design-system')) {
                            return 'design-system'
                        }

                        // 其他第三方库
                        if (id.includes('node_modules')) {
                            return 'vendor'
                        }
                    },

                    // 文件命名
                    chunkFileNames: (chunkInfo) => {
                        const facadeModuleId = chunkInfo.facadeModuleId
                        if (facadeModuleId) {
                            const fileName = facadeModuleId.split('/').pop()
                            return `js/${fileName}-[hash].js`
                        }
                        return 'js/[name]-[hash].js'
                    },

                    entryFileNames: 'js/[name]-[hash].js',
                    assetFileNames: (assetInfo) => {
                        const info = assetInfo.name.split('.')
                        const ext = info[info.length - 1]

                        if (/\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/i.test(assetInfo.name)) {
                            return `media/[name]-[hash].${ext}`
                        }

                        if (/\.(png|jpe?g|gif|svg)(\?.*)?$/i.test(assetInfo.name)) {
                            return `images/[name]-[hash].${ext}`
                        }

                        if (/\.(woff2?|eot|ttf|otf)(\?.*)?$/i.test(assetInfo.name)) {
                            return `fonts/[name]-[hash].${ext}`
                        }

                        return `assets/[name]-[hash].${ext}`
                    }
                }
            }
        },

        // 开发服务器配置
        server: {
            host: '0.0.0.0',
            port: parseInt(env.VITE_DEV_PORT) || 3000,
            open: false,
            cors: true,

            // 代理配置
            proxy: {
                '/api': {
                    target: env.VITE_API_URL || 'http://127.0.0.1:18001',
                    changeOrigin: true,
                    secure: false,
                    rewrite: (path) => path.replace(/^\/api/, '/api')
                },
                '/data': {
                    target: env.VITE_API_URL || 'http://127.0.0.1:18001',
                    changeOrigin: true,
                    secure: false,
                    rewrite: (path) => path.replace(/^\/data/, '/data')
                }
            },

            // HMR配置
            hmr: {
                overlay: true
            }
        },

        // 预览服务器配置
        preview: {
            host: '0.0.0.0',
            port: parseInt(env.VITE_PREVIEW_PORT) || 3000,
            cors: true
        },

        // 优化配置
        optimizeDeps: {
            include: [
                'vue',
                'vue-router',
                'pinia',
                'element-plus',
                '@element-plus/icons-vue',
                'axios'
            ],
            exclude: [
                '@vitejs/plugin-vue'
            ]
        },

        // CSS配置
        css: {
            preprocessorOptions: {
                scss: {
                    additionalData: `@use "@/styles/variables.scss" as *;`
                }
            },
            devSourcemap: isDevelopment
        },

        // 环境变量配置
        define: {
            __VUE_OPTIONS_API__: true,
            __VUE_PROD_DEVTOOLS__: false,
            __APP_VERSION__: JSON.stringify(process.env.npm_package_version || '1.0.0'),
            __BUILD_TIME__: JSON.stringify(new Date().toISOString())
        }
    }
})